<?php

/**
 * Simple example demonstrating CleaningBusinessScraper usage
 * 
 * This script shows how to use the standalone service class
 * to scrape cleaning businesses by address.
 */

require_once 'LaravelIntegrationExample.php';

// Example 1: Basic usage with error handling
function example1() {
    echo "=== Example 1: Basic Usage ===\n";
    
    try {
        $scraper = new CleaningBusinessScraper();
        
        // Check if system is ready
        $status = $scraper->validateSystem();
        if (!$status['requirements_met']) {
            echo "❌ System not ready. Errors:\n";
            foreach ($status['errors'] as $error) {
                echo "  - $error\n";
            }
            return;
        }
        
        // Scrape cleaning businesses
        $address = 'Mountain Brook, AL';
        $maxResults = 5;
        
        echo "🔍 Searching for cleaning businesses near: $address\n";
        $result = $scraper->scrapeCleaningBusinesses($address, $maxResults);
        
        if ($result['success']) {
            echo "✅ Found {$result['results']['total_found']} businesses\n";
            echo "⏱️  Execution time: {$result['results']['execution_time_ms']}ms\n\n";
            
            // Display first business as example
            if (!empty($result['results']['businesses'])) {
                $business = $result['results']['businesses'][0];
                echo "📋 First business:\n";
                echo "  Name: {$business['name']}\n";
                echo "  Address: {$business['address']}\n";
                echo "  Phone: {$business['phone']}\n";
                echo "  Category: {$business['category']}\n";
                if (!empty($business['services'])) {
                    echo "  Services: " . implode(', ', $business['services']) . "\n";
                }
            }
        } else {
            echo "❌ Scraping failed: {$result['error']['message']}\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Example 2: Using static method
function example2() {
    echo "=== Example 2: Static Method Usage ===\n";
    
    try {
        $address = 'Birmingham, AL';
        $landmark = 'UAB Hospital';
        
        echo "🔍 Quick search near $landmark, $address\n";
        $result = CleaningBusinessScraper::scrape($address, 3, $landmark);
        
        if ($result['success']) {
            echo "✅ Found {$result['results']['total_found']} businesses\n";
            echo "📍 Search query: {$result['query']['address']}\n";
            echo "🏢 Landmark: {$result['query']['landmark']}\n";
        } else {
            echo "❌ No results: {$result['error']['message']}\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Example 3: JSON output for API usage
function example3() {
    echo "=== Example 3: JSON API Response ===\n";
    
    try {
        $result = CleaningBusinessScraper::scrape('Hoover, AL', 2);
        
        // This is how you'd return JSON in an API
        header('Content-Type: application/json');
        echo json_encode($result, JSON_PRETTY_PRINT);
        
    } catch (Exception $e) {
        $errorResponse = [
            'success' => false,
            'error' => [
                'message' => $e->getMessage(),
                'type' => 'SYSTEM_ERROR'
            ],
            'timestamp' => date('c')
        ];
        
        header('Content-Type: application/json');
        echo json_encode($errorResponse, JSON_PRETTY_PRINT);
    }
}

// Run examples
if (php_sapi_name() === 'cli') {
    // Command line usage
    echo "🧹 Cleaning Business Scraper - Usage Examples\n\n";
    
    example1();
    example2();
    
    echo "=== Example 3: JSON Output ===\n";
    echo "For JSON output, run this script in a web browser or remove the CLI check.\n\n";
    
} else {
    // Web usage - return JSON
    example3();
}
