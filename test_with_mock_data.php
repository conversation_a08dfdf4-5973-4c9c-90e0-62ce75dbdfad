<?php

/**
 * Test new format with mock data to verify structure
 */

require_once 'LaravelIntegrationExample.php';

echo "=== Testing New Format with Mock Data ===\n\n";

// Create a custom scraper class for testing with mock data
class MockCleaningBusinessScraper extends CleaningBusinessScraper
{
    public function scrapeCleaningBusinesses($address, $maxResults = 15, $landmark = null)
    {
        // Mock data that simulates what we would get from Google Maps
        $mockBusinesses = [
            [
                'name' => 'A 24 Hour Home & Office Cleaning Service',
                'address' => '2100 Southbridge Pkwy #650, Birmingham, AL',
                'phone' => '************',
                'website' => 'http://www.a24hourcleaningservice.com/',
                'rating' => '4.9',
                'reviews' => '57',
                'category' => 'General Business'
            ],
            [
                'name' => 'Utopia Cleaners & Laundry',
                'address' => '123 Main St, Mountain Brook, AL',
                'phone' => '************',
                'website' => 'http://www.utopiacleaners.com/',
                'rating' => '4.8',
                'reviews' => '123',
                'category' => 'General Business'
            ],
            [
                'name' => 'Champion Cleaners',
                'address' => '42 Church St, Mountain Brook, AL',
                'phone' => '************',
                'website' => 'http://www.championcleaners.com/',
                'rating' => '4.9',
                'reviews' => '150',
                'category' => 'General Business'
            ]
        ];
        
        // Transform to new structure
        $transformedBusinesses = [];
        foreach ($mockBusinesses as $business) {
            $transformedBusinesses[] = $this->transformBusinessStructure($business);
        }
        
        return $transformedBusinesses;
    }
    
    // Make the method public for testing
    public function transformBusinessStructure($business)
    {
        return parent::transformBusinessStructure($business);
    }
}

try {
    $scraper = new MockCleaningBusinessScraper();
    
    echo "Testing with mock data for Mountain Brook, AL...\n\n";
    
    $result = $scraper->scrapeCleaningBusinesses('Mountain Brook, AL', 5);
    
    if (!empty($result)) {
        echo "✅ SUCCESS! Found " . count($result) . " cleaning businesses\n\n";
        
        echo "📄 NEW FORMAT OUTPUT:\n";
        echo json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
        
        echo "📋 BUSINESS DETAILS:\n";
        foreach ($result as $i => $business) {
            echo "\n" . ($i + 1) . ". " . $business['name'] . "\n";
            echo "   📍 Address: " . ($business['address'] ?: 'Not available') . "\n";
            echo "   📞 Phone: " . ($business['phone'] ?: 'Not available') . "\n";
            echo "   🌐 Website: " . ($business['website'] ?: 'Not available') . "\n";
            echo "   🏷️  Category: " . $business['category'] . "\n";
            echo "   📍 Location: " . $business['location'] . "\n";
            echo "   📧 Email: " . ($business['email'] ?: 'Not available') . "\n";
            echo "   🕒 Hours: " . (empty($business['hours']) ? 'Not available' : implode(', ', $business['hours'])) . "\n";
            echo "   📸 Photos: " . (empty($business['photos']) ? 'Not available' : count($business['photos']) . ' photos') . "\n";
            echo "   🧹 Services: " . implode(', ', $business['services']) . "\n";
            echo "   ⭐ Reviews: " . (empty($business['reviews']) ? 'Not available' : count($business['reviews']) . ' reviews') . "\n";
            echo "   🗺️  Coordinates: " . $business['lat'] . ", " . $business['lng'] . "\n";
            
            if (!empty($business['reviews'])) {
                echo "   📝 Sample Review: " . $business['reviews'][0]['text'] . "\n";
                echo "      Rating: " . $business['reviews'][0]['rating'] . "/5\n";
                echo "      Author: " . $business['reviews'][0]['author'] . "\n";
                echo "      Date: " . $business['reviews'][0]['date'] . "\n";
            }
        }
        
        echo "\n=== Format Validation ===\n";
        
        // Validate the format matches the required structure
        $sample = $result[0];
        $requiredFields = [
            'name', 'address', 'phone', 'website', 'category', 
            'location', 'email', 'hours', 'photos', 'services', 
            'reviews', 'lat', 'lng'
        ];
        
        $missingFields = [];
        foreach ($requiredFields as $field) {
            if (!array_key_exists($field, $sample)) {
                $missingFields[] = $field;
            }
        }
        
        if (empty($missingFields)) {
            echo "✅ All required fields present\n";
            echo "✅ Format matches specification\n";
            echo "✅ Structure is correct\n";
        } else {
            echo "❌ Missing fields: " . implode(', ', $missingFields) . "\n";
        }
        
        // Check if reviews have correct structure
        if (!empty($sample['reviews'])) {
            $review = $sample['reviews'][0];
            $reviewFields = ['text', 'rating', 'author', 'date'];
            $missingReviewFields = [];
            
            foreach ($reviewFields as $field) {
                if (!array_key_exists($field, $review)) {
                    $missingReviewFields[] = $field;
                }
            }
            
            if (empty($missingReviewFields)) {
                echo "✅ Review structure is correct\n";
            } else {
                echo "❌ Missing review fields: " . implode(', ', $missingReviewFields) . "\n";
            }
        }
        
        echo "\n=== Example Usage ===\n";
        echo "// Get cleaning businesses\n";
        echo "\$businesses = CleaningBusinessScraper::scrape('Mountain Brook, AL', 10);\n\n";
        echo "// Access business data\n";
        echo "foreach (\$businesses as \$business) {\n";
        echo "    echo \$business['name'] . \"\\n\";\n";
        echo "    echo \$business['phone'] . \"\\n\";\n";
        echo "    echo \$business['address'] . \"\\n\";\n";
        echo "    echo \"Services: \" . implode(', ', \$business['services']) . \"\\n\";\n";
        echo "}\n";
        
    } else {
        echo "❌ No cleaning businesses found\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
