# Cleaning Business Scraper Service

A standalone PHP service class for scraping cleaning business data by address. This service can run independently without any Laravel framework dependencies.

## Features

- 🏢 Scrape cleaning businesses by address
- 🎯 Filter results specifically for cleaning services
- 📍 Optional landmark-based searching
- ⚡ Fast execution with configurable timeouts
- 🔍 Intelligent business categorization
- 📊 Detailed JSON response format
- 🛠️ Built-in system validation
- 🚀 Static methods for quick usage

## Requirements

- PHP 7.4 or higher
- Node.js (for the scraping engine)
- Required Node.js packages (see package.json)

## Installation

1. Ensure Node.js is installed:
```bash
node --version
```

2. Install Node.js dependencies:
```bash
npm install
```

3. Verify the scraper script exists:
   - `scraper.js` (main scraper)
   - `laravel-scraper.js` (Laravel integration wrapper)

## Usage

### Basic Usage

```php
<?php
require_once 'LaravelIntegrationExample.php';

// Create scraper instance
$scraper = new CleaningBusinessScraper();

// Scrape cleaning businesses
$result = $scraper->scrapeCleaningBusinesses('Mountain Brook, AL', 10);

// Output results
echo json_encode($result, JSON_PRETTY_PRINT);
```

### Quick Static Method

```php
// One-liner for quick scraping
$result = CleaningBusinessScraper::scrape('Birmingham, AL', 15);
```

### With Landmark

```php
// Search near a specific landmark
$result = CleaningBusinessScraper::scrape('Birmingham, AL', 20, 'UAB Hospital');
```

### System Validation

```php
$scraper = new CleaningBusinessScraper();
$status = $scraper->validateSystem();

if (!$status['requirements_met']) {
    echo "System requirements not met:\n";
    foreach ($status['errors'] as $error) {
        echo "- $error\n";
    }
}
```

### Command Line Usage

```bash
php -r "
require_once 'LaravelIntegrationExample.php';
\$result = CleaningBusinessScraper::scrape('Mountain Brook, AL', 5);
echo json_encode(\$result, JSON_PRETTY_PRINT);
"
```

## Response Format

```json
{
  "success": true,
  "timestamp": "2024-01-15T10:30:00+00:00",
  "query": {
    "address": "Mountain Brook, AL",
    "landmark": null,
    "max_results": 10,
    "search_type": "cleaning_businesses"
  },
  "results": {
    "total_found": 8,
    "execution_time_ms": 15420.5,
    "businesses": [
      {
        "name": "Mountain Brook Cleaning Service",
        "address": "123 Main St, Mountain Brook, AL",
        "phone": "(*************",
        "website": "https://example.com",
        "rating": 4.8,
        "reviews": 156,
        "category": "Residential Cleaning",
        "services": ["Residential Cleaning", "Deep Cleaning"]
      }
    ]
  },
  "meta": {
    "scraper_version": "1.0.0",
    "data_source": "google_maps"
  }
}
```

## Testing

Run the test script to verify everything is working:

```bash
php test_scraper.php
```

## Configuration

The scraper can be configured with custom options:

```php
$scraper = new CleaningBusinessScraper('/path/to/custom/scraper.js', 300); // Custom script path and timeout
```

## Error Handling

The service includes comprehensive error handling:

- Input validation
- System requirement checks
- Timeout management
- JSON parsing validation
- Process execution monitoring

## Business Categories

The scraper automatically categorizes cleaning businesses:

- Carpet Cleaning
- Window Cleaning
- Pressure Washing
- Maid Service
- Janitorial Service
- Commercial Cleaning
- Residential Cleaning
- Office Cleaning
- General Cleaning Service

## Troubleshooting

1. **"Node.js not available"**: Install Node.js and ensure it's in your PATH
2. **"Scraper script not found"**: Ensure scraper.js exists in the project directory
3. **"Process timeout"**: Increase timeout value or check internet connection
4. **"No results found"**: Try different search terms or increase max_results

## License

This project is open source and available under the MIT License.
