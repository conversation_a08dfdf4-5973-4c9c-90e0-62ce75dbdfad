<?php

/**
 * Test script for CleaningBusinessScraper
 * 
 * This script demonstrates how to use the CleaningBusinessScraper service class
 * Run: php test_scraper.php
 */

require_once 'LaravelIntegrationExample.php';

echo "=== Cleaning Business Scraper Test ===\n\n";

// Test 1: Validate system requirements
echo "1. Checking system requirements...\n";
$scraper = new CleaningBusinessScraper();
$status = $scraper->validateSystem();

echo "PHP Version: " . $status['php_version'] . "\n";
echo "Node.js Available: " . ($status['node_available'] ? 'Yes' : 'No') . "\n";
echo "Script Found: " . ($status['script_found'] ? 'Yes' : 'No') . "\n";
echo "Script Path: " . ($status['script_path'] ?: 'Not found') . "\n";
echo "Requirements Met: " . ($status['requirements_met'] ? 'Yes' : 'No') . "\n";

if (!empty($status['errors'])) {
    echo "Errors:\n";
    foreach ($status['errors'] as $error) {
        echo "  - $error\n";
    }
}

echo "\n";

// Test 2: Get scraper stats
echo "2. Scraper statistics:\n";
$stats = $scraper->getStats();
foreach ($stats as $key => $value) {
    echo "  $key: " . (is_bool($value) ? ($value ? 'true' : 'false') : $value) . "\n";
}

echo "\n";

// Test 3: Only proceed with scraping if requirements are met
if ($status['requirements_met']) {
    echo "3. Testing scraper with sample address...\n";
    
    // Test with a sample address
    $testAddress = 'Mountain Brook, AL';
    $maxResults = 5;
    
    echo "Searching for cleaning businesses near: $testAddress\n";
    echo "Max results: $maxResults\n";
    echo "Please wait...\n\n";
    
    try {
        $result = CleaningBusinessScraper::scrape($testAddress, $maxResults);
        
        echo "=== SCRAPING RESULTS ===\n";
        echo json_encode($result, JSON_PRETTY_PRINT);
        echo "\n";
        
        if ($result['success']) {
            echo "✅ Success! Found " . $result['results']['total_found'] . " cleaning businesses\n";
            echo "Execution time: " . $result['results']['execution_time_ms'] . "ms\n";
        } else {
            echo "❌ Scraping failed: " . $result['error']['message'] . "\n";
        }
        
    } catch (Exception $e) {
        echo "❌ Error occurred: " . $e->getMessage() . "\n";
    }
    
} else {
    echo "3. ❌ Cannot test scraping - system requirements not met\n";
    echo "Please ensure:\n";
    echo "  - Node.js is installed and available in PATH\n";
    echo "  - scraper.js or laravel-scraper.js file exists in the same directory\n";
    echo "  - npm dependencies are installed (run: npm install)\n";
}

echo "\n=== Test Complete ===\n";
