<?php

/**
 * Debug script for new output format
 */

require_once 'LaravelIntegrationExample.php';

echo "=== Debug New Output Format ===\n";

try {
    $scraper = new CleaningBusinessScraper();
    
    // Check system first
    $status = $scraper->validateSystem();
    echo "System status: " . ($status['requirements_met'] ? 'OK' : 'FAILED') . "\n";
    
    if (!$status['requirements_met']) {
        foreach ($status['errors'] as $error) {
            echo "Error: $error\n";
        }
        exit(1);
    }
    
    echo "Script path: " . $status['script_path'] . "\n\n";
    
    // Test with try-catch to see what's happening
    echo "Testing scrapeCleaningBusinesses method...\n";
    
    $startTime = microtime(true);
    $result = $scraper->scrapeCleaningBusinesses('Mountain Brook, AL', 5);
    $endTime = microtime(true);
    
    echo "Execution time: " . round(($endTime - $startTime) * 1000, 2) . "ms\n";
    echo "Result type: " . gettype($result) . "\n";
    echo "Result count: " . (is_array($result) ? count($result) : 'N/A') . "\n";
    
    if (is_array($result)) {
        if (empty($result)) {
            echo "Result is empty array\n";
        } else {
            echo "Result has " . count($result) . " items\n";
            echo "First item keys: " . implode(', ', array_keys($result[0])) . "\n";
            
            echo "\nFull result:\n";
            echo json_encode($result, JSON_PRETTY_PRINT) . "\n";
        }
    } else {
        echo "Result is not an array: " . var_export($result, true) . "\n";
    }
    
} catch (Exception $e) {
    echo "Exception caught: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
    echo "Trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Complete ===\n";
