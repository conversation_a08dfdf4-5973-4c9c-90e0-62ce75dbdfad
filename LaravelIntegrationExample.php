<?php

/**
 * Cleaning Business Scraper Service
 * 
 * Standalone service class để scrape cleaning businesses theo địa chỉ
 * Không phụ thuộc vào Laravel framework, models hay controllers
 * 
 * Usage:
 * $scraper = new CleaningBusinessScraper();
 * $result = $scraper->scrapeCleaningBusinesses('Mountain Brook, AL', 10);
 * echo json_encode($result, JSON_PRETTY_PRINT);
 */

class CleaningBusinessScraper
{
    private $scriptPath;
    private $timeout;
    private $userAgent;

    public function __construct($scriptPath = null, $timeout = 180)
    {
        $this->scriptPath = $scriptPath ?: $this->findScriptPath();
        $this->timeout = $timeout;
        $this->userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    }

    /**
     * Scrape cleaning businesses theo địa chỉ
     * 
     * @param string $address Địa chỉ cần tìm kiếm
     * @param int $maxResults Số lượng kết quả tối đa (default: 15)
     * @param string|null $landmark Landmark gần đó (optional)
     * @return array JSON response với danh sách cleaning businesses
     */
    public function scrapeCleaningBusinesses($address, $maxResults = 15, $landmark = null)
    {
        $startTime = microtime(true);
        
        try {
            $this->validateInputs($address, $maxResults);
            
            $scrapedData = $this->runScraper($address, $landmark, $maxResults);
            
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            if ($scrapedData['success'] && !empty($scrapedData['results']['businesses'])) {
                $cleaningBusinesses = $this->filterCleaningBusinesses($scrapedData['results']['businesses']);
                
                return [
                    'success' => true,
                    'timestamp' => date('c'),
                    'query' => [
                        'address' => $address,
                        'landmark' => $landmark,
                        'max_results' => $maxResults,
                        'search_type' => 'cleaning_businesses'
                    ],
                    'results' => [
                        'total_found' => count($cleaningBusinesses),
                        'execution_time_ms' => $executionTime,
                        'businesses' => $cleaningBusinesses
                    ],
                    'meta' => [
                        'scraper_version' => '1.0.0',
                        'data_source' => 'google_maps'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'timestamp' => date('c'),
                    'query' => [
                        'address' => $address,
                        'landmark' => $landmark,
                        'max_results' => $maxResults,
                        'search_type' => 'cleaning_businesses'
                    ],
                    'error' => [
                        'message' => $scrapedData['error']['message'] ?? 'No cleaning businesses found',
                        'type' => 'NO_RESULTS_FOUND'
                    ],
                    'results' => [
                        'total_found' => 0,
                        'execution_time_ms' => $executionTime,
                        'businesses' => []
                    ]
                ];
            }
            
        } catch (Exception $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => false,
                'timestamp' => date('c'),
                'query' => [
                    'address' => $address,
                    'landmark' => $landmark,
                    'max_results' => $maxResults,
                    'search_type' => 'cleaning_businesses'
                ],
                'error' => [
                    'message' => $e->getMessage(),
                    'type' => 'SCRAPER_ERROR'
                ],
                'results' => [
                    'total_found' => 0,
                    'execution_time_ms' => $executionTime,
                    'businesses' => []
                ]
            ];
        }
    }

    /**
     * Validate input parameters
     */
    private function validateInputs($address, $maxResults)
    {
        if (empty($address) || strlen(trim($address)) < 3) {
            throw new Exception('Address must be at least 3 characters long');
        }

        if (!is_int($maxResults) || $maxResults < 1 || $maxResults > 50) {
            throw new Exception('Max results must be between 1 and 50');
        }

        if (!file_exists($this->scriptPath)) {
            throw new Exception('Scraper script not found at: ' . $this->scriptPath);
        }
    }

    /**
     * Run the Node.js scraper script
     */
    private function runScraper($address, $landmark, $maxResults)
    {
        // Modify search query to focus on cleaning businesses
        $searchQuery = $landmark 
            ? "cleaning services near {$landmark}, {$address}"
            : "cleaning services near {$address}";

        $command = [
            'node',
            $this->scriptPath,
            $searchQuery
        ];
        
        // Only add landmark if it's not null
        if ($landmark !== null) {
            $command[] = $landmark;
        } else {
            $command[] = ''; // Add empty string instead of null
        }
        
        $command[] = (string) $maxResults;

        // Create process
        $descriptorspec = [
            0 => ["pipe", "r"],  // stdin
            1 => ["pipe", "w"],  // stdout
            2 => ["pipe", "w"]   // stderr
        ];

        $process = proc_open(implode(' ', array_map('escapeshellarg', $command)), $descriptorspec, $pipes);

        if (!is_resource($process)) {
            throw new Exception('Failed to start scraper process');
        }

        // Close stdin
        fclose($pipes[0]);

        // Set timeout for reading
        $startTime = time();
        $output = '';
        $errorOutput = '';

        while (time() - $startTime < $this->timeout) {
            $status = proc_get_status($process);
            
            if (!$status['running']) {
                break;
            }
            
            usleep(100000); // Sleep 0.1 second
        }

        // Read output
        $output = stream_get_contents($pipes[1]);
        $errorOutput = stream_get_contents($pipes[2]);

        fclose($pipes[1]);
        fclose($pipes[2]);

        $exitCode = proc_close($process);

        if ($exitCode !== 0) {
            $errorData = json_decode($errorOutput, true);
            throw new Exception(
                $errorData['error']['message'] ?? 'Scraper process failed with exit code: ' . $exitCode
            );
        }

        $result = json_decode($output, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON response from scraper: ' . json_last_error_msg());
        }

        return $result;
    }

    /**
     * Filter businesses to focus on cleaning services
     */
    private function filterCleaningBusinesses($businesses)
    {
        $cleaningKeywords = [
            'clean', 'cleaning', 'cleaner', 'cleaners', 'maid', 'maids', 
            'housekeeping', 'janitorial', 'custodial', 'sanitiz', 'disinfect',
            'carpet clean', 'window clean', 'pressure wash', 'steam clean',
            'residential clean', 'commercial clean', 'office clean', 'home clean'
        ];

        $filteredBusinesses = [];

        foreach ($businesses as $business) {
            $businessText = strtolower($business['name'] . ' ' . ($business['category'] ?? ''));
            $isCleaningBusiness = false;

            // Check if business name or category contains cleaning keywords
            foreach ($cleaningKeywords as $keyword) {
                if (strpos($businessText, $keyword) !== false) {
                    $isCleaningBusiness = true;
                    break;
                }
            }

            if ($isCleaningBusiness) {
                // Enhance business data
                $business['category'] = $this->determineCleaningCategory($business['name']);
                $business['services'] = $this->extractCleaningServices($business['name']);
                $filteredBusinesses[] = $business;
            }
        }

        // If no specific cleaning businesses found, return all businesses
        // (they might be cleaning businesses but not clearly labeled)
        if (empty($filteredBusinesses) && !empty($businesses)) {
            return array_map(function($business) {
                $business['category'] = 'General Cleaning Service';
                $business['services'] = ['General Cleaning'];
                return $business;
            }, $businesses);
        }

        return $filteredBusinesses;
    }

    /**
     * Determine specific cleaning category
     */
    private function determineCleaningCategory($businessName)
    {
        $name = strtolower($businessName);

        if (strpos($name, 'carpet') !== false) return 'Carpet Cleaning';
        if (strpos($name, 'window') !== false) return 'Window Cleaning';
        if (strpos($name, 'pressure') !== false || strpos($name, 'power wash') !== false) return 'Pressure Washing';
        if (strpos($name, 'maid') !== false) return 'Maid Service';
        if (strpos($name, 'janitorial') !== false) return 'Janitorial Service';
        if (strpos($name, 'commercial') !== false) return 'Commercial Cleaning';
        if (strpos($name, 'residential') !== false || strpos($name, 'home') !== false) return 'Residential Cleaning';
        if (strpos($name, 'office') !== false) return 'Office Cleaning';

        return 'General Cleaning Service';
    }

    /**
     * Extract cleaning services from business name
     */
    private function extractCleaningServices($businessName)
    {
        $services = [];
        $name = strtolower($businessName);

        $serviceMap = [
            'carpet' => 'Carpet Cleaning',
            'window' => 'Window Cleaning',
            'pressure' => 'Pressure Washing',
            'steam' => 'Steam Cleaning',
            'upholstery' => 'Upholstery Cleaning',
            'tile' => 'Tile & Grout Cleaning',
            'maid' => 'Maid Service',
            'housekeeping' => 'Housekeeping',
            'janitorial' => 'Janitorial Service',
            'office' => 'Office Cleaning',
            'commercial' => 'Commercial Cleaning',
            'residential' => 'Residential Cleaning'
        ];

        foreach ($serviceMap as $keyword => $service) {
            if (strpos($name, $keyword) !== false) {
                $services[] = $service;
            }
        }

        return !empty($services) ? $services : ['General Cleaning'];
    }

    /**
     * Find scraper script path
     */
    private function findScriptPath()
    {
        $possiblePaths = [
            __DIR__ . '/clean-scraper.js',
            __DIR__ . '/laravel-scraper.js',
            __DIR__ . '/../clean-scraper.js',
            __DIR__ . '/../laravel-scraper.js',
            __DIR__ . '/../../clean-scraper.js',
            __DIR__ . '/../../laravel-scraper.js',
            getcwd() . '/clean-scraper.js',
            getcwd() . '/laravel-scraper.js',
            './clean-scraper.js',
            './laravel-scraper.js'
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return realpath($path);
            }
        }

        throw new Exception('Could not find clean-scraper.js or laravel-scraper.js script. Please specify the path manually.');
    }

    /**
     * Get scraper statistics
     */
    public function getStats()
    {
        return [
            'script_path' => $this->scriptPath,
            'timeout' => $this->timeout,
            'script_exists' => file_exists($this->scriptPath),
            'node_available' => $this->isNodeAvailable()
        ];
    }

    /**
     * Check if Node.js is available
     */
    private function isNodeAvailable()
    {
        $output = shell_exec('node --version 2>&1');
        return strpos($output, 'v') === 0;
    }
}

// Usage example in your controller:
/*
class BusinessController extends Controller
{
    public function searchBusinesses(Request $request)
    {
        $location = $request->input('location');
        $landmark = $request->input('landmark');
        $maxResults = $request->input('max_results', 15);

        // Dispatch the job
        $job = new ScrapeBusiness($location, $landmark, $maxResults);
        
        // For immediate execution (synchronous)
        $businesses = $job->handle();
        
        // Or for queue execution (asynchronous)
        // dispatch($job);
        
        return response()->json([
            'success' => true,
            'businesses' => $businesses
        ]);
    }
}
*/