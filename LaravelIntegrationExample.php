<?php

/**
 * Cleaning Business Scraper Service
 *
 * Standalone service class to scrape cleaning businesses by address
 * No dependencies on Laravel framework, models, or controllers
 * Requires Node.js and the scraper.js file to be present
 *
 * Usage:
 * $scraper = new CleaningBusinessScraper();
 * $result = $scraper->scrapeCleaningBusinesses('Mountain Brook, AL', 10);
 * echo json_encode($result, JSON_PRETTY_PRINT);
 *
 * Example Response:
 * {
 *   "success": true,
 *   "timestamp": "2024-01-15T10:30:00+00:00",
 *   "query": {
 *     "address": "Mountain Brook, AL",
 *     "landmark": null,
 *     "max_results": 10,
 *     "search_type": "cleaning_businesses"
 *   },
 *   "results": {
 *     "total_found": 8,
 *     "execution_time_ms": 15420.5,
 *     "businesses": [...]
 *   }
 * }
 */

class CleaningBusinessScraper
{
    private $scriptPath;
    private $timeout;
    private $userAgent;

    public function __construct($scriptPath = null, $timeout = 180)
    {
        $this->scriptPath = $scriptPath ?: $this->findScriptPath();
        $this->timeout = $timeout;
        $this->userAgent = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36';
    }

    /**
     * Scrape cleaning businesses by address
     *
     * @param string $address Address to search for cleaning businesses
     * @param int $maxResults Maximum number of results to return (default: 15)
     * @param string|null $landmark Optional landmark near the address
     * @return array JSON response with list of cleaning businesses
     */
    public function scrapeCleaningBusinesses($address, $maxResults = 15, $landmark = null)
    {
        $startTime = microtime(true);
        
        try {
            $this->validateInputs($address, $maxResults);
            
            $scrapedData = $this->runScraper($address, $landmark, $maxResults);
            
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            if ($scrapedData['success'] && !empty($scrapedData['results']['businesses'])) {
                $cleaningBusinesses = $this->filterCleaningBusinesses($scrapedData['results']['businesses']);
                
                return [
                    'success' => true,
                    'timestamp' => date('c'),
                    'query' => [
                        'address' => $address,
                        'landmark' => $landmark,
                        'max_results' => $maxResults,
                        'search_type' => 'cleaning_businesses'
                    ],
                    'results' => [
                        'total_found' => count($cleaningBusinesses),
                        'execution_time_ms' => $executionTime,
                        'businesses' => $cleaningBusinesses
                    ],
                    'meta' => [
                        'scraper_version' => '1.0.0',
                        'data_source' => 'google_maps'
                    ]
                ];
            } else {
                return [
                    'success' => false,
                    'timestamp' => date('c'),
                    'query' => [
                        'address' => $address,
                        'landmark' => $landmark,
                        'max_results' => $maxResults,
                        'search_type' => 'cleaning_businesses'
                    ],
                    'error' => [
                        'message' => $scrapedData['error']['message'] ?? 'No cleaning businesses found',
                        'type' => 'NO_RESULTS_FOUND'
                    ],
                    'results' => [
                        'total_found' => 0,
                        'execution_time_ms' => $executionTime,
                        'businesses' => []
                    ]
                ];
            }
            
        } catch (Exception $e) {
            $executionTime = round((microtime(true) - $startTime) * 1000, 2);
            
            return [
                'success' => false,
                'timestamp' => date('c'),
                'query' => [
                    'address' => $address,
                    'landmark' => $landmark,
                    'max_results' => $maxResults,
                    'search_type' => 'cleaning_businesses'
                ],
                'error' => [
                    'message' => $e->getMessage(),
                    'type' => 'SCRAPER_ERROR'
                ],
                'results' => [
                    'total_found' => 0,
                    'execution_time_ms' => $executionTime,
                    'businesses' => []
                ]
            ];
        }
    }

    /**
     * Validate input parameters
     */
    private function validateInputs($address, $maxResults)
    {
        if (empty($address) || strlen(trim($address)) < 3) {
            throw new Exception('Address must be at least 3 characters long');
        }

        if (!is_int($maxResults) || $maxResults < 1 || $maxResults > 50) {
            throw new Exception('Max results must be between 1 and 50');
        }

        if (!file_exists($this->scriptPath)) {
            throw new Exception('Scraper script not found at: ' . $this->scriptPath);
        }
    }

    /**
     * Run the Node.js scraper script
     */
    private function runScraper($address, $landmark, $maxResults)
    {
        // Modify search query to focus on cleaning businesses
        $searchQuery = $landmark 
            ? "cleaning services near {$landmark}, {$address}"
            : "cleaning services near {$address}";

        $command = [
            'node',
            $this->scriptPath,
            $searchQuery
        ];
        
        // Only add landmark if it's not null
        if ($landmark !== null) {
            $command[] = $landmark;
        } else {
            $command[] = ''; // Add empty string instead of null
        }
        
        $command[] = (string) $maxResults;

        // Create process
        $descriptorspec = [
            0 => ["pipe", "r"],  // stdin
            1 => ["pipe", "w"],  // stdout
            2 => ["pipe", "w"]   // stderr
        ];

        $process = proc_open(implode(' ', array_map('escapeshellarg', $command)), $descriptorspec, $pipes);

        if (!is_resource($process)) {
            throw new Exception('Failed to start scraper process');
        }

        // Close stdin
        fclose($pipes[0]);

        // Set timeout for reading
        $startTime = time();
        $output = '';
        $errorOutput = '';

        while (time() - $startTime < $this->timeout) {
            $status = proc_get_status($process);
            
            if (!$status['running']) {
                break;
            }
            
            usleep(100000); // Sleep 0.1 second
        }

        // Read output
        $output = stream_get_contents($pipes[1]);
        $errorOutput = stream_get_contents($pipes[2]);

        fclose($pipes[1]);
        fclose($pipes[2]);

        $exitCode = proc_close($process);

        if ($exitCode !== 0) {
            $errorData = json_decode($errorOutput, true);
            throw new Exception(
                $errorData['error']['message'] ?? 'Scraper process failed with exit code: ' . $exitCode
            );
        }

        // Debug: Log raw output for troubleshooting
        if (empty($output)) {
            throw new Exception('Empty response from scraper. Error output: ' . $errorOutput);
        }

        // Clean the output and parse JSON
        $output = trim($output);

        // Try to find JSON in the output (in case there are any remaining console.log statements)
        $lines = explode("\n", $output);
        $jsonLine = '';

        // Look for the last line that starts with { (the actual JSON response)
        for ($i = count($lines) - 1; $i >= 0; $i--) {
            $line = trim($lines[$i]);
            if (!empty($line) && substr($line, 0, 1) === '{') {
                $jsonLine = $line;
                break;
            }
        }

        // If no JSON line found, try the entire output
        if (empty($jsonLine)) {
            $jsonLine = $output;
        }

        $result = json_decode($jsonLine, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            throw new Exception('Invalid JSON response from scraper: ' . json_last_error_msg() . '. Raw output: ' . substr($output, 0, 300) . '...');
        }

        return $result;
    }

    /**
     * Filter businesses to focus on cleaning services
     */
    private function filterCleaningBusinesses($businesses)
    {
        $cleaningKeywords = [
            'clean', 'cleaning', 'cleaner', 'cleaners', 'maid', 'maids', 
            'housekeeping', 'janitorial', 'custodial', 'sanitiz', 'disinfect',
            'carpet clean', 'window clean', 'pressure wash', 'steam clean',
            'residential clean', 'commercial clean', 'office clean', 'home clean'
        ];

        $filteredBusinesses = [];

        foreach ($businesses as $business) {
            $businessText = strtolower($business['name'] . ' ' . ($business['category'] ?? ''));
            $isCleaningBusiness = false;

            // Check if business name or category contains cleaning keywords
            foreach ($cleaningKeywords as $keyword) {
                if (strpos($businessText, $keyword) !== false) {
                    $isCleaningBusiness = true;
                    break;
                }
            }

            if ($isCleaningBusiness) {
                // Enhance business data
                $business['category'] = $this->determineCleaningCategory($business['name']);
                $business['services'] = $this->extractCleaningServices($business['name']);
                $filteredBusinesses[] = $business;
            }
        }

        // If no specific cleaning businesses found, return all businesses
        // (they might be cleaning businesses but not clearly labeled)
        if (empty($filteredBusinesses) && !empty($businesses)) {
            return array_map(function($business) {
                $business['category'] = 'General Cleaning Service';
                $business['services'] = ['General Cleaning'];
                return $business;
            }, $businesses);
        }

        return $filteredBusinesses;
    }

    /**
     * Determine specific cleaning category
     */
    private function determineCleaningCategory($businessName)
    {
        $name = strtolower($businessName);

        if (strpos($name, 'carpet') !== false) return 'Carpet Cleaning';
        if (strpos($name, 'window') !== false) return 'Window Cleaning';
        if (strpos($name, 'pressure') !== false || strpos($name, 'power wash') !== false) return 'Pressure Washing';
        if (strpos($name, 'maid') !== false) return 'Maid Service';
        if (strpos($name, 'janitorial') !== false) return 'Janitorial Service';
        if (strpos($name, 'commercial') !== false) return 'Commercial Cleaning';
        if (strpos($name, 'residential') !== false || strpos($name, 'home') !== false) return 'Residential Cleaning';
        if (strpos($name, 'office') !== false) return 'Office Cleaning';

        return 'General Cleaning Service';
    }

    /**
     * Extract cleaning services from business name
     */
    private function extractCleaningServices($businessName)
    {
        $services = [];
        $name = strtolower($businessName);

        $serviceMap = [
            'carpet' => 'Carpet Cleaning',
            'window' => 'Window Cleaning',
            'pressure' => 'Pressure Washing',
            'steam' => 'Steam Cleaning',
            'upholstery' => 'Upholstery Cleaning',
            'tile' => 'Tile & Grout Cleaning',
            'maid' => 'Maid Service',
            'housekeeping' => 'Housekeeping',
            'janitorial' => 'Janitorial Service',
            'office' => 'Office Cleaning',
            'commercial' => 'Commercial Cleaning',
            'residential' => 'Residential Cleaning'
        ];

        foreach ($serviceMap as $keyword => $service) {
            if (strpos($name, $keyword) !== false) {
                $services[] = $service;
            }
        }

        return !empty($services) ? $services : ['General Cleaning'];
    }

    /**
     * Find scraper script path
     */
    private function findScriptPath()
    {
        $possiblePaths = [
            __DIR__ . '/clean-scraper.js',
            __DIR__ . '/laravel-scraper.js',
            __DIR__ . '/scraper.js',
            __DIR__ . '/../clean-scraper.js',
            __DIR__ . '/../laravel-scraper.js',
            __DIR__ . '/../scraper.js',
            __DIR__ . '/../../clean-scraper.js',
            __DIR__ . '/../../laravel-scraper.js',
            __DIR__ . '/../../scraper.js',
            getcwd() . '/clean-scraper.js',
            getcwd() . '/laravel-scraper.js',
            getcwd() . '/scraper.js',
            './clean-scraper.js',
            './laravel-scraper.js',
            './scraper.js'
        ];

        foreach ($possiblePaths as $path) {
            if (file_exists($path)) {
                return realpath($path);
            }
        }

        throw new Exception('Could not find scraper.js, laravel-scraper.js, or clean-scraper.js script. Please ensure the Node.js scraper script is available.');
    }

    /**
     * Get scraper statistics
     */
    public function getStats()
    {
        return [
            'script_path' => $this->scriptPath,
            'timeout' => $this->timeout,
            'script_exists' => file_exists($this->scriptPath),
            'node_available' => $this->isNodeAvailable()
        ];
    }

    /**
     * Check if Node.js is available
     */
    private function isNodeAvailable()
    {
        $output = shell_exec('node --version 2>&1');
        return strpos($output, 'v') === 0;
    }

    /**
     * Static method for quick scraping without instantiation
     *
     * @param string $address Address to search for cleaning businesses
     * @param int $maxResults Maximum number of results (default: 15)
     * @param string|null $landmark Optional landmark
     * @return array JSON response with cleaning businesses
     */
    public static function scrape($address, $maxResults = 15, $landmark = null)
    {
        $scraper = new self();
        return $scraper->scrapeCleaningBusinesses($address, $maxResults, $landmark);
    }

    /**
     * Validate system requirements
     *
     * @return array System status information
     */
    public function validateSystem()
    {
        $status = [
            'php_version' => PHP_VERSION,
            'node_available' => $this->isNodeAvailable(),
            'script_found' => false,
            'script_path' => null,
            'requirements_met' => false,
            'errors' => []
        ];

        try {
            $status['script_path'] = $this->findScriptPath();
            $status['script_found'] = true;
        } catch (Exception $e) {
            $status['errors'][] = $e->getMessage();
        }

        if (!$status['node_available']) {
            $status['errors'][] = 'Node.js is not available. Please install Node.js to use this scraper.';
        }

        $status['requirements_met'] = $status['node_available'] && $status['script_found'];

        return $status;
    }
}

/*
 * USAGE EXAMPLES:
 *
 * 1. Basic usage:
 * $scraper = new CleaningBusinessScraper();
 * $result = $scraper->scrapeCleaningBusinesses('Mountain Brook, AL', 10);
 * echo json_encode($result, JSON_PRETTY_PRINT);
 *
 * 2. Quick static method:
 * $result = CleaningBusinessScraper::scrape('Birmingham, AL', 15);
 *
 * 3. With landmark:
 * $result = CleaningBusinessScraper::scrape('Birmingham, AL', 20, 'UAB Hospital');
 *
 * 4. Check system requirements:
 * $scraper = new CleaningBusinessScraper();
 * $status = $scraper->validateSystem();
 * if (!$status['requirements_met']) {
 *     echo "System requirements not met:\n";
 *     foreach ($status['errors'] as $error) {
 *         echo "- $error\n";
 *     }
 * }
 *
 * 5. Get scraper statistics:
 * $stats = $scraper->getStats();
 * print_r($stats);
 *
 * 6. Command line usage:
 * php -r "
 * require_once 'LaravelIntegrationExample.php';
 * \$result = CleaningBusinessScraper::scrape('Mountain Brook, AL', 5);
 * echo json_encode(\$result, JSON_PRETTY_PRINT);
 * "
 */