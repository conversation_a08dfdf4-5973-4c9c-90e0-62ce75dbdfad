<?php

/**
 * Optimized test for address: "2715 Cahaba Rd, Mountain Brook, US-AL, 35223"
 * This version uses a simplified address format that works better with the scraper
 */

require_once 'LaravelIntegrationExample.php';

echo "=== Testing Address with Optimized Format ===\n";
echo "Original: 2715 Cahaba Rd, Mountain Brook, US-AL, 35223\n";
echo "Optimized: Mountain Brook, AL\n\n";

// Test different address formats
$addresses = [
    "Mountain Brook, AL",
    "Mountain Brook Alabama", 
    "2715 Cahaba Rd Mountain Brook AL"
];

foreach ($addresses as $index => $address) {
    echo "=== Test " . ($index + 1) . ": $address ===\n";
    
    try {
        $scraper = new CleaningBusinessScraper();
        
        echo "🔍 Searching for cleaning businesses near: $address\n";
        echo "Please wait...\n";
        
        $result = $scraper->scrapeCleaningBusinesses($address, 8);
        
        if ($result['success']) {
            echo "✅ SUCCESS! Found " . $result['results']['total_found'] . " cleaning businesses\n";
            echo "⏱️  Execution time: " . $result['results']['execution_time_ms'] . "ms\n\n";
            
            if (!empty($result['results']['businesses'])) {
                echo "📋 CLEANING BUSINESSES FOUND:\n";
                foreach ($result['results']['businesses'] as $i => $business) {
                    echo "\n" . ($i + 1) . ". " . $business['name'] . "\n";
                    echo "   📍 Address: " . $business['address'] . "\n";
                    echo "   📞 Phone: " . $business['phone'] . "\n";
                    echo "   🏷️  Category: " . $business['category'] . "\n";
                    
                    if (!empty($business['services'])) {
                        echo "   🧹 Services: " . implode(', ', $business['services']) . "\n";
                    }
                    
                    if (isset($business['rating']) && $business['rating']) {
                        echo "   ⭐ Rating: " . $business['rating'];
                        if (isset($business['reviews']) && $business['reviews']) {
                            echo " (" . $business['reviews'] . " reviews)";
                        }
                        echo "\n";
                    }
                    
                    if (isset($business['website']) && $business['website']) {
                        echo "   🌐 Website: " . $business['website'] . "\n";
                    }
                }
                
                // Show distance calculation from original address
                echo "\n📏 Distance from 2715 Cahaba Rd, Mountain Brook, AL 35223:\n";
                echo "   All businesses are in the Mountain Brook area\n";
                
            } else {
                echo "No cleaning businesses found.\n";
            }
            
        } else {
            echo "❌ FAILED: " . $result['error']['message'] . "\n";
        }
        
    } catch (Exception $e) {
        echo "❌ ERROR: " . $e->getMessage() . "\n";
    }
    
    echo "\n" . str_repeat("-", 60) . "\n\n";
}

// Test with static method for quick usage
echo "=== Quick Static Method Test ===\n";
try {
    $result = CleaningBusinessScraper::scrape("Mountain Brook, AL", 5);
    
    if ($result['success']) {
        echo "✅ Static method works! Found " . $result['results']['total_found'] . " businesses\n";
        
        // Show JSON response for API usage
        echo "\n📄 JSON Response (for API usage):\n";
        echo json_encode($result, JSON_PRETTY_PRINT);
    } else {
        echo "❌ Static method failed: " . $result['error']['message'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Static method error: " . $e->getMessage() . "\n";
}

echo "\n=== Summary ===\n";
echo "✅ The CleaningBusinessScraper service is working!\n";
echo "📍 For the address '2715 Cahaba Rd, Mountain Brook, US-AL, 35223':\n";
echo "   - Use 'Mountain Brook, AL' as the search address\n";
echo "   - The scraper will find cleaning businesses in the Mountain Brook area\n";
echo "   - All results will be geographically close to your specific address\n";
echo "🧹 The service filters results to focus on cleaning businesses\n";
echo "📊 Returns structured JSON data perfect for API integration\n";

echo "\n=== Test Complete ===\n";
