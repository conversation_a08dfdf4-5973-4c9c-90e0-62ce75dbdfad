<?php

/**
 * Test script for new output format
 * Address: "2715 Cahaba Rd, Mountain Brook, US-AL, 35223"
 */

require_once 'LaravelIntegrationExample.php';

echo "=== Testing New Output Format ===\n";
echo "Address: 2715 Cahaba Rd, Mountain Brook, US-AL, 35223\n";
echo "Search: Mountain Brook, AL\n\n";

try {
    $scraper = new CleaningBusinessScraper();
    
    // Test the new format
    $result = $scraper->scrapeCleaningBusinesses('Mountain Brook, AL', 5);
    
    if (!empty($result)) {
        echo "✅ SUCCESS! Found " . count($result) . " cleaning businesses\n\n";
        
        echo "📄 NEW FORMAT OUTPUT:\n";
        echo json_encode($result, JSON_PRETTY_PRINT) . "\n\n";
        
        echo "📋 BUSINESS DETAILS:\n";
        foreach ($result as $i => $business) {
            echo "\n" . ($i + 1) . ". " . $business['name'] . "\n";
            echo "   📍 Address: " . ($business['address'] ?: 'Not available') . "\n";
            echo "   📞 Phone: " . ($business['phone'] ?: 'Not available') . "\n";
            echo "   🌐 Website: " . ($business['website'] ?: 'Not available') . "\n";
            echo "   🏷️  Category: " . $business['category'] . "\n";
            echo "   📍 Location: " . $business['location'] . "\n";
            echo "   📧 Email: " . ($business['email'] ?: 'Not available') . "\n";
            echo "   🕒 Hours: " . (empty($business['hours']) ? 'Not available' : implode(', ', $business['hours'])) . "\n";
            echo "   📸 Photos: " . (empty($business['photos']) ? 'Not available' : count($business['photos']) . ' photos') . "\n";
            echo "   🧹 Services: " . implode(', ', $business['services']) . "\n";
            echo "   ⭐ Reviews: " . (empty($business['reviews']) ? 'Not available' : count($business['reviews']) . ' reviews') . "\n";
            echo "   🗺️  Coordinates: " . $business['lat'] . ", " . $business['lng'] . "\n";
            
            if (!empty($business['reviews'])) {
                echo "   📝 Sample Review: " . $business['reviews'][0]['text'] . "\n";
                echo "      Rating: " . $business['reviews'][0]['rating'] . "/5\n";
                echo "      Author: " . $business['reviews'][0]['author'] . "\n";
                echo "      Date: " . $business['reviews'][0]['date'] . "\n";
            }
        }
        
    } else {
        echo "❌ No cleaning businesses found\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== Format Validation ===\n";

// Validate the format matches the required structure
if (!empty($result)) {
    $sample = $result[0];
    $requiredFields = [
        'name', 'address', 'phone', 'website', 'category', 
        'location', 'email', 'hours', 'photos', 'services', 
        'reviews', 'lat', 'lng'
    ];
    
    $missingFields = [];
    foreach ($requiredFields as $field) {
        if (!array_key_exists($field, $sample)) {
            $missingFields[] = $field;
        }
    }
    
    if (empty($missingFields)) {
        echo "✅ All required fields present\n";
        echo "✅ Format matches specification\n";
        echo "✅ Ready for production use\n";
    } else {
        echo "❌ Missing fields: " . implode(', ', $missingFields) . "\n";
    }
    
    // Check if reviews have correct structure
    if (!empty($sample['reviews'])) {
        $review = $sample['reviews'][0];
        $reviewFields = ['text', 'rating', 'author', 'date'];
        $missingReviewFields = [];
        
        foreach ($reviewFields as $field) {
            if (!array_key_exists($field, $review)) {
                $missingReviewFields[] = $field;
            }
        }
        
        if (empty($missingReviewFields)) {
            echo "✅ Review structure is correct\n";
        } else {
            echo "❌ Missing review fields: " . implode(', ', $missingReviewFields) . "\n";
        }
    }
}

echo "\n=== Test Complete ===\n";
