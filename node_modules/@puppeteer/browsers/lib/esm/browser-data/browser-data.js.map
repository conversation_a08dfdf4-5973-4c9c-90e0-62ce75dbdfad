{"version": 3, "file": "browser-data.js", "sourceRoot": "", "sources": ["../../../src/browser-data/browser-data.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,OAAO,KAAK,MAAM,MAAM,aAAa,CAAC;AACtC,OAAO,KAAK,YAAY,MAAM,mBAAmB,CAAC;AAClD,OAAO,KAAK,QAAQ,MAAM,eAAe,CAAC;AAC1C,OAAO,KAAK,OAAO,MAAM,cAAc,CAAC;AACxC,OAAO,EACL,OAAO,EACP,eAAe,EACf,UAAU,EACV,oBAAoB,GAErB,MAAM,YAAY,CAAC;AAIpB,MAAM,CAAC,MAAM,YAAY,GAAG;IAC1B,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,kBAAkB;IACvD,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,kBAAkB;IAC3C,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,kBAAkB;IAC/C,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,kBAAkB;CAC9C,CAAC;AAEF,MAAM,CAAC,MAAM,aAAa,GAAG;IAC3B,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,mBAAmB;IACxD,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,mBAAmB;IAC5C,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,mBAAmB;IAChD,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,mBAAmB;CAC/C,CAAC;AAEF,MAAM,CAAC,MAAM,uBAAuB,GAAG;IACrC,CAAC,OAAO,CAAC,YAAY,CAAC,EAAE,YAAY,CAAC,sBAAsB;IAC3D,CAAC,OAAO,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,sBAAsB;IAC/C,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,sBAAsB;IACnD,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,sBAAsB;CAClD,CAAC;AAEF,OAAO,EAAC,OAAO,EAAE,eAAe,EAAE,oBAAoB,EAAC,CAAC;AAExD;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,cAAc,CAClC,OAAgB,EAChB,QAAyB,EACzB,GAAW;IAEX,QAAQ,OAAO,EAAE;QACf,KAAK,OAAO,CAAC,OAAO;YAClB,QAAQ,GAAiB,EAAE;gBACzB,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,OAAO,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;gBACzD,KAAK,UAAU,CAAC,IAAI,CAAC;gBACrB,KAAK,UAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,UAAU,CAAC,GAAG,CAAC;gBACpB,KAAK,UAAU,CAAC,MAAM;oBACpB,MAAM,IAAI,KAAK,CACb,GAAG,GAAG,yBAAyB,OAAO,yBAAyB,CAChE,CAAC;aACL;QACH,KAAK,OAAO,CAAC,MAAM;YACjB,QAAQ,GAAiB,EAAE;gBACzB,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,MAAM,CAAC,cAAc,CAChC,QAAQ,EACR,oBAAoB,CAAC,MAAM,CAC5B,CAAC;gBACJ,KAAK,UAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,MAAM,CAAC,cAAc,CAChC,QAAQ,EACR,oBAAoB,CAAC,IAAI,CAC1B,CAAC;gBACJ,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,MAAM,CAAC,cAAc,CAChC,QAAQ,EACR,oBAAoB,CAAC,MAAM,CAC5B,CAAC;gBACJ,KAAK,UAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,MAAM,CAAC,cAAc,CAChC,QAAQ,EACR,oBAAoB,CAAC,GAAG,CACzB,CAAC;gBACJ,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,MAAM,CAAC,cAAc,CAChC,QAAQ,EACR,oBAAoB,CAAC,MAAM,CAC5B,CAAC;aACL;QACH,KAAK,OAAO,CAAC,YAAY;YACvB,QAAQ,GAAiB,EAAE;gBACzB,KAAK,UAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;gBACxE,KAAK,UAAU,CAAC,IAAI;oBAClB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,oBAAoB,CAAC,IAAI,CAAC,CAAC;gBACtE,KAAK,UAAU,CAAC,GAAG;oBACjB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC;gBACrE,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,YAAY,CAAC,cAAc,CAAC,oBAAoB,CAAC,MAAM,CAAC,CAAC;aACzE;QACH,KAAK,OAAO,CAAC,QAAQ;YACnB,QAAQ,GAAiB,EAAE;gBACzB,KAAK,UAAU,CAAC,MAAM;oBACpB,OAAO,MAAM,QAAQ,CAAC,cAAc,CAAC,QAAQ,CAAC,CAAC;gBACjD,KAAK,UAAU,CAAC,IAAI,CAAC;gBACrB,KAAK,UAAU,CAAC,MAAM,CAAC;gBACvB,KAAK,UAAU,CAAC,GAAG,CAAC;gBACpB,KAAK,UAAU,CAAC,MAAM;oBACpB,MAAM,IAAI,KAAK,CACb,GAAG,GAAG,yBAAyB,OAAO,yBAAyB,CAChE,CAAC;aACL;KACJ;IACD,oEAAoE;IACpE,OAAO,GAAG,CAAC;AACb,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,aAAa,CACjC,OAAgB,EAChB,IAAoB;IAEpB,QAAQ,OAAO,EAAE;QACf,KAAK,OAAO,CAAC,OAAO;YAClB,OAAO,MAAM,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;QAC3C,KAAK,OAAO,CAAC,MAAM,CAAC;QACpB,KAAK,OAAO,CAAC,QAAQ;YACnB,MAAM,IAAI,KAAK,CAAC,uCAAuC,OAAO,MAAM,CAAC,CAAC;KACzE;AACH,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,2BAA2B,CACzC,OAAgB,EAChB,QAAyB,EACzB,OAA6B;IAE7B,QAAQ,OAAO,EAAE;QACf,KAAK,OAAO,CAAC,YAAY,CAAC;QAC1B,KAAK,OAAO,CAAC,OAAO,CAAC;QACrB,KAAK,OAAO,CAAC,QAAQ;YACnB,MAAM,IAAI,KAAK,CACb,iDAAiD,OAAO,OAAO,CAChE,CAAC;QACJ,KAAK,OAAO,CAAC,MAAM;YACjB,OAAO,MAAM,CAAC,2BAA2B,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAC;KAChE;AACH,CAAC"}