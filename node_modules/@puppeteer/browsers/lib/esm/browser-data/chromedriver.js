/**
 * Copyright 2023 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import path from 'path';
import { getLastKnownGoodReleaseForChannel } from './chrome.js';
import { BrowserPlatform } from './types.js';
function folder(platform) {
    switch (platform) {
        case BrowserPlatform.LINUX:
            return 'linux64';
        case BrowserPlatform.MAC_ARM:
            return 'mac-arm64';
        case BrowserPlatform.MAC:
            return 'mac-x64';
        case BrowserPlatform.WIN32:
            return 'win32';
        case BrowserPlatform.WIN64:
            return 'win64';
    }
}
export function resolveDownloadUrl(platform, buildId, baseUrl = 'https://edgedl.me.gvt1.com/edgedl/chrome/chrome-for-testing') {
    return `${baseUrl}/${resolveDownloadPath(platform, buildId).join('/')}`;
}
export function resolveDownloadPath(platform, buildId) {
    return [buildId, folder(platform), `chromedriver-${folder(platform)}.zip`];
}
export function relativeExecutablePath(platform, _buildId) {
    switch (platform) {
        case BrowserPlatform.MAC:
        case BrowserPlatform.MAC_ARM:
            return path.join('chromedriver-' + folder(platform), 'chromedriver');
        case BrowserPlatform.LINUX:
            return path.join('chromedriver-linux64', 'chromedriver');
        case BrowserPlatform.WIN32:
        case BrowserPlatform.WIN64:
            return path.join('chromedriver-' + folder(platform), 'chromedriver.exe');
    }
}
export async function resolveBuildId(channel) {
    return (await getLastKnownGoodReleaseForChannel(channel)).version;
}
//# sourceMappingURL=chromedriver.js.map