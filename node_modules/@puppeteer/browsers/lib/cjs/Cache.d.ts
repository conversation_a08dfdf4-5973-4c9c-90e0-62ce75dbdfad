/**
 * Copyright 2023 Google Inc. All rights reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Browser, BrowserPlatform } from './browser-data/browser-data.js';
/**
 * @public
 */
export interface InstalledBrowser {
    /**
     * Path to the root of the installation folder. Use
     * {@link computeExecutablePath} to get the path to the executable binary.
     */
    path: string;
    browser: Browser;
    buildId: string;
    platform: BrowserPlatform;
}
/**
 * The cache used by Puppeteer relies on the following structure:
 *
 * - rootDir
 *   -- <browser1> | browserRoot(browser1)
 *   ---- <platform>-<buildId> | installationDir()
 *   ------ the browser-platform-buildId
 *   ------ specific structure.
 *   -- <browser2> | browserRoot(browser2)
 *   ---- <platform>-<buildId> | installationDir()
 *   ------ the browser-platform-buildId
 *   ------ specific structure.
 *   @internal
 */
export declare class Cache {
    #private;
    constructor(rootDir: string);
    browserRoot(browser: Browser): string;
    installationDir(browser: Browser, platform: BrowserPlatform, buildId: string): string;
    clear(): void;
    uninstall(browser: Browser, platform: BrowserPlatform, buildId: string): void;
    getInstalledBrowsers(): InstalledBrowser[];
}
//# sourceMappingURL=Cache.d.ts.map