#!/usr/bin/env node

const BusinessScraper = require("./scraper");

/**
 * Clean Laravel Job Integration Script (No Debug Output)
 * Usage: node clean-scraper.js <location> [landmark] [maxResults]
 */

// Suppress console.log from the scraper module
const originalConsoleLog = console.log;
console.log = function() {
  // Only allow JSON output
  const message = arguments[0];
  if (typeof message === 'string' && (message.startsWith('{') || message.startsWith('['))) {
    originalConsoleLog.apply(console, arguments);
  }
  // Suppress all other console.log messages
};

async function runScraper() {
  try {
    // Parse command line arguments
    const location = process.argv[2];
    const landmark = process.argv[3] || null;
    const maxResults = parseInt(process.argv[4]) || 15;

    if (!location) {
      throw new Error("Location parameter is required");
    }

    // Initialize scraper with optimized settings for Laravel jobs
    const scraper = new BusinessScraper({
      maxBusinesses: maxResults,
      timeout: 20000, // Reduced timeout for faster execution
    });

    // Scrape businesses
    const businesses = await scraper.scrapeBusinesses(location, landmark);

    // Filter out businesses without essential information
    const validBusinesses = businesses.filter(
      (business) => business.name && 
      business.name !== "Collapse side panel" && 
      business.name !== "Rating" &&
      (business.address || business.phone)
    );

    // Return structured response for Laravel
    const response = {
      success: true,
      timestamp: new Date().toISOString(),
      query: {
        location: location,
        landmark: landmark,
        maxResults: maxResults,
      },
      results: {
        total: validBusinesses.length,
        businesses: validBusinesses.map((business) => ({
          name: business.name,
          address: business.address || "Address not available",
          phone: business.phone || "Phone not available",
          website: business.website || null,
          rating: business.rating || null,
          reviews: business.reviews || null,
          category: business.category || "General Business",
        })),
      },
    };

    // Output JSON response only
    originalConsoleLog(JSON.stringify(response));
  } catch (error) {
    // Error response for Laravel
    const errorResponse = {
      success: false,
      timestamp: new Date().toISOString(),
      error: {
        message: error.message,
        type: error.name || "ScrapingError",
      },
      query: {
        location: process.argv[2] || null,
        landmark: process.argv[3] || null,
        maxResults: parseInt(process.argv[4]) || 15,
      },
      results: {
        total: 0,
        businesses: [],
      },
    };

    originalConsoleLog(JSON.stringify(errorResponse));
    process.exit(1);
  }
}

// Execute the scraper
runScraper();
