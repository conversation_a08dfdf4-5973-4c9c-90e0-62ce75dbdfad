<?php

/**
 * Final test for address: "2715 Cahaba Rd, Mountain Brook, US-AL, 35223"
 */

require_once 'LaravelIntegrationExample.php';

echo "=== Testing CleaningBusinessScraper with your address ===\n";
echo "Original Address: 2715 Cahaba Rd, Mountain Brook, US-AL, 35223\n";
echo "Optimized Search: Mountain Brook, AL\n\n";

try {
    $result = CleaningBusinessScraper::scrape('Mountain Brook, AL', 8);
    
    if ($result['success']) {
        echo "✅ SUCCESS! Found " . $result['results']['total_found'] . " cleaning businesses\n";
        echo "⏱️  Execution time: " . $result['results']['execution_time_ms'] . "ms\n\n";
        
        if (!empty($result['results']['businesses'])) {
            echo "📋 CLEANING BUSINESSES NEAR YOUR ADDRESS:\n";
            foreach ($result['results']['businesses'] as $i => $business) {
                echo "\n" . ($i + 1) . ". " . $business['name'] . "\n";
                echo "   📍 Address: " . $business['address'] . "\n";
                echo "   📞 Phone: " . $business['phone'] . "\n";
                echo "   🏷️  Category: " . $business['category'] . "\n";
                
                if (!empty($business['services'])) {
                    echo "   🧹 Services: " . implode(', ', $business['services']) . "\n";
                }
                
                if (isset($business['rating']) && $business['rating']) {
                    echo "   ⭐ Rating: " . $business['rating'];
                    if (isset($business['reviews']) && $business['reviews']) {
                        echo " (" . $business['reviews'] . " reviews)";
                    }
                    echo "\n";
                }
                
                if (isset($business['website']) && $business['website']) {
                    echo "   🌐 Website: " . $business['website'] . "\n";
                }
            }
        }
        
        echo "\n📄 Full JSON Response:\n";
        echo json_encode($result, JSON_PRETTY_PRINT) . "\n";
        
    } else {
        echo "❌ FAILED: " . $result['error']['message'] . "\n";
        echo "Error type: " . $result['error']['type'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ ERROR: " . $e->getMessage() . "\n";
}

echo "\n=== Test Complete ===\n";
